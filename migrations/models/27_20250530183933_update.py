from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "authorized" ALTER COLUMN "status" TYPE VARCHAR(9) USING "status"::VARCHAR(9);
        COMMENT ON COLUMN "authorized"."status" IS 'ACTIVE: active
COMPLETED: completed
PENDING: pending
REJECTED: rejected
DELETED: deleted';
        ALTER TABLE "cohort" ADD "status" VARCHAR(9) NOT NULL  DEFAULT 'active';
        ALTER TABLE "participants" ALTER COLUMN "status" TYPE VARCHAR(9) USING "status"::VARCHAR(9);
        COMMENT ON COLUMN "participants"."status" IS 'ACTIVE: active
COMPLETED: completed
PENDING: pending
REJECTED: rejected
DELETED: deleted';
        ALTER TABLE "solera_participants" ALTER COLUMN "status" TYPE VARCHAR(9) USING "status"::VARCHAR(9);
        COMMENT ON COLUMN "solera_participants"."status" IS 'ACTIVE: active
COMPLETED: completed
PENDING: pending
REJECTED: rejected
DELETED: deleted';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "cohort" DROP COLUMN "status";
        COMMENT ON COLUMN "authorized"."status" IS 'ACTIVE: active
PENDING: pending
REJECTED: rejected
DELETED: deleted';
        ALTER TABLE "authorized" ALTER COLUMN "status" TYPE VARCHAR(8) USING "status"::VARCHAR(8);
        COMMENT ON COLUMN "participants"."status" IS 'ACTIVE: active
PENDING: pending
REJECTED: rejected
DELETED: deleted';
        ALTER TABLE "participants" ALTER COLUMN "status" TYPE VARCHAR(8) USING "status"::VARCHAR(8);
        COMMENT ON COLUMN "solera_participants"."status" IS 'ACTIVE: active
PENDING: pending
REJECTED: rejected
DELETED: deleted';
        ALTER TABLE "solera_participants" ALTER COLUMN "status" TYPE VARCHAR(8) USING "status"::VARCHAR(8);"""
